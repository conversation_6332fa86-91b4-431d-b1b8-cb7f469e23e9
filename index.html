<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Chore Assignment System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }

        /* Navigation Tabs */
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow-x: auto;
        }

        .tab {
            flex: 1;
            min-width: 150px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab:hover {
            background-color: #f8f9fa;
        }

        .tab.active {
            background-color: #007bff;
            color: white;
        }

        .tab:first-child {
            border-radius: 10px 0 0 10px;
        }

        .tab:last-child {
            border-radius: 0 10px 10px 0;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        /* Buttons */
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }

        .btn:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
        }

        .btn.btn-large {
            padding: 20px 40px;
            font-size: 18px;
            border-radius: 10px;
            width: 100%;
            margin-bottom: 20px;
        }

        .btn.btn-danger {
            background-color: #dc3545;
        }

        .btn.btn-danger:hover {
            background-color: #c82333;
        }

        .btn.btn-secondary {
            background-color: #6c757d;
        }

        .btn.btn-secondary:hover {
            background-color: #545b62;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .form-check input {
            margin-right: 10px;
            transform: scale(1.2);
        }

        /* Slider */
        .slider {
            width: 100%;
            margin-bottom: 10px;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider-value {
            min-width: 60px;
            text-align: center;
            font-weight: bold;
            color: #007bff;
        }

        /* Grid Layouts */
        .family-grid, .job-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .family-member, .job-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .family-member h4, .job-item h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .job-points {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .job-points.easy { background: #28a745; }
        .job-points.medium { background: #ffc107; color: #333; }
        .job-points.hard { background: #dc3545; }

        /* Roster Display */
        .roster-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .person-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .person-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .person-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #007bff;
        }

        .person-points {
            background: #007bff;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
        }

        .day-schedule {
            margin-bottom: 15px;
        }

        .day-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
            text-transform: uppercase;
        }

        .day-jobs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .job-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        .job-tag.easy { background: #28a745; }
        .job-tag.medium { background: #ffc107; color: #333; }
        .job-tag.hard { background: #dc3545; }

        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        /* Print Styles */
        @media print {
            body { 
                background: white; 
                color: black;
                font-size: 12px;
            }
            .container { 
                max-width: none; 
                padding: 10px;
            }
            .tabs, .btn, .no-print { 
                display: none !important; 
            }
            .card { 
                box-shadow: none; 
                border: 1px solid #ddd;
                margin-bottom: 10px;
                page-break-inside: avoid;
            }
            .roster-grid {
                display: block;
            }
            .person-card {
                margin-bottom: 15px;
                page-break-inside: avoid;
            }
            h1 {
                font-size: 18px;
                margin-bottom: 15px;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                border-radius: 0;
                border-bottom: 1px solid #e9ecef;
            }
            
            .tab:first-child {
                border-radius: 10px 10px 0 0;
            }
            
            .tab:last-child {
                border-radius: 0 0 10px 10px;
                border-bottom: none;
            }
            
            .roster-grid {
                grid-template-columns: 1fr;
            }
            
            .slider-container {
                flex-direction: column;
                align-items: stretch;
            }
        }

        /* Radio button styling */
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .radio-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .radio-option input[type="radio"] {
            margin-right: 10px;
        }

        .radio-option.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }

        /* History styles */
        .history-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-date {
            font-weight: bold;
            color: #333;
        }

        .history-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        /* Balance summary */
        .balance-summary {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .balance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Family Chore Assignment System</h1>
        
        <!-- Navigation Tabs -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('generate')">🎲 Generate Roster</button>
            <button class="tab" onclick="showTab('family')">👨‍👩‍👧‍👦 Manage Family</button>
            <button class="tab" onclick="showTab('jobs')">🧹 Manage Jobs</button>
            <button class="tab" onclick="showTab('settings')">⚙️ Settings</button>
            <button class="tab" onclick="showTab('history')">📋 View History</button>
        </div>

        <!-- Generate Roster Tab -->
        <div id="generate" class="tab-content active">
            <div class="card">
                <button class="btn btn-large" onclick="generateRoster()">🎲 Generate Weekly Roster</button>
                <div id="roster-results"></div>
            </div>
        </div>

        <!-- Manage Family Tab -->
        <div id="family" class="tab-content">
            <div class="card">
                <h2>Add Family Member</h2>
                <div class="form-group">
                    <label for="family-name">Name</label>
                    <input type="text" id="family-name" class="form-control" placeholder="Enter family member name">
                </div>
                <button class="btn" onclick="addFamilyMember()">Add Family Member</button>
            </div>
            
            <div class="card">
                <h2>Family Members</h2>
                <div id="family-list"></div>
            </div>
        </div>

        <!-- Manage Jobs Tab -->
        <div id="jobs" class="tab-content">
            <div class="card">
                <h2>Add Job</h2>
                <div class="form-group">
                    <label for="job-name">Job Name</label>
                    <input type="text" id="job-name" class="form-control" placeholder="e.g., Clean Bathroom">
                </div>
                
                <div class="form-group">
                    <label>Scoring Method</label>
                    <div class="radio-group">
                        <div class="radio-option" onclick="selectScoringMethod('manual')">
                            <input type="radio" name="scoring" value="manual" checked>
                            <span>🎯 Set my own score (1-10)</span>
                        </div>
                        <div class="radio-option" onclick="selectScoringMethod('wizard')">
                            <input type="radio" name="scoring" value="wizard">
                            <span>🧙‍♂️ Use scoring wizard</span>
                        </div>
                    </div>
                </div>

                <div id="manual-scoring">
                    <div class="form-group">
                        <label for="job-points">Points (1-10)</label>
                        <div class="slider-container">
                            <input type="range" id="job-points" class="slider" min="1" max="10" value="5">
                            <span id="points-value" class="slider-value">5</span>
                        </div>
                    </div>
                </div>

                <div id="wizard-scoring" style="display: none;">
                    <div class="form-group">
                        <label>How long does this job take?</label>
                        <select id="time-effort" class="form-control">
                            <option value="1">Quick (&lt;5 min)</option>
                            <option value="2">Medium (5-20 min)</option>
                            <option value="3">Long (20+ min)</option>
                            <option value="4">Very long (1+ hour)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Physical effort required?</label>
                        <select id="physical-effort" class="form-control">
                            <option value="1">Easy/sitting</option>
                            <option value="2">Light</option>
                            <option value="3">Moderate</option>
                            <option value="4">Heavy</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>How unpleasant?</label>
                        <select id="unpleasantness" class="form-control">
                            <option value="1">Enjoyable</option>
                            <option value="2">Neutral</option>
                            <option value="3">Somewhat unpleasant</option>
                            <option value="4">Really gross</option>
                        </select>
                    </div>
                    <div id="wizard-calculation"></div>
                </div>
                
                <div class="form-group">
                    <label for="job-frequency">Frequency</label>
                    <select id="job-frequency" class="form-control">
                        <option value="Daily">Daily</option>
                        <option value="Every 2 days">Every 2 days</option>
                        <option value="Every 3 days">Every 3 days</option>
                        <option value="Weekly">Weekly</option>
                        <option value="Monthly">Monthly</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="has-exemptions" onchange="toggleExemptions()">
                        <label for="has-exemptions">Some people should never do this job</label>
                    </div>
                    <div id="exemptions-list" style="display: none;"></div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="has-dependency" onchange="toggleDependency()">
                        <label for="has-dependency">Requires another job to be done first</label>
                    </div>
                    <div id="dependency-controls" style="display: none;">
                        <div class="form-group">
                            <label for="pre-job">Required Job</label>
                            <select id="pre-job" class="form-control"></select>
                        </div>
                        <div class="form-group">
                            <label for="pre-job-timing">Timing</label>
                            <select id="pre-job-timing" class="form-control">
                                <option value="same-day">Same day</option>
                                <option value="next-day">Next day</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <button class="btn" id="job-submit-btn" onclick="addJob()">Add Job</button>
                <button class="btn btn-secondary" id="job-cancel-btn" onclick="cancelJobEdit()" style="display: none; margin-left: 10px;">Cancel Edit</button>
            </div>
            
            <div class="card">
                <h2>Jobs</h2>
                <div id="jobs-list"></div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="card">
                <h2>Balancing Strength</h2>
                
                <div class="form-group">
                    <label for="workload-balance">Workload Balance Strength (0.1-1.0)</label>
                    <div class="slider-container">
                        <input type="range" id="workload-balance" class="slider" min="0.1" max="1.0" step="0.1" value="0.5">
                        <span id="workload-value" class="slider-value">0.5</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="recent-penalty">Recent Assignment Penalty (0.1-2.0)</label>
                    <div class="slider-container">
                        <input type="range" id="recent-penalty" class="slider" min="0.1" max="2.0" step="0.1" value="0.3">
                        <span id="recent-value" class="slider-value">0.3</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="randomness">Randomness Factor (0.0-1.0)</label>
                    <div class="slider-container">
                        <input type="range" id="randomness" class="slider" min="0.0" max="1.0" step="0.1" value="0.8">
                        <span id="randomness-value" class="slider-value">0.8</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>Assignment Rules</h2>
                
                <div class="form-group">
                    <label for="history-weeks">History Tracking (1-5 weeks)</label>
                    <div class="slider-container">
                        <input type="range" id="history-weeks" class="slider" min="1" max="5" step="1" value="3">
                        <span id="history-value" class="slider-value">3</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="split-threshold">Job Split Threshold (3-15 points)</label>
                    <div class="slider-container">
                        <input type="range" id="split-threshold" class="slider" min="3" max="15" step="1" value="8">
                        <span id="split-value" class="slider-value">8</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>Daily Balance Controls</h2>
                
                <div class="form-group">
                    <label for="max-points-per-day">Max Points Per Day (4-20)</label>
                    <div class="slider-container">
                        <input type="range" id="max-points-per-day" class="slider" min="4" max="20" step="1" value="10">
                        <span id="max-points-value" class="slider-value">10</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="daily-balance-strictness">Daily Balance Strictness (0.3-1.0)</label>
                    <div class="slider-container">
                        <input type="range" id="daily-balance-strictness" class="slider" min="0.3" max="1.0" step="0.1" value="0.7">
                        <span id="daily-strictness-value" class="slider-value">0.7</span>
                    </div>
                </div>
                
                <div style="display: flex; gap: 15px; margin-top: 20px;">
                    <button class="btn" onclick="saveSettings()">Save Settings</button>
                    <button class="btn btn-secondary" onclick="resetSettings()">Reset to Defaults</button>
                </div>
            </div>
        </div>

        <!-- View History Tab -->
        <div id="history" class="tab-content">
            <div class="card">
                <h2>Recent Rosters</h2>
                <div id="history-list"></div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let familyMembers = [];
        let jobs = [];
        let history = [];
        let editingJobId = null; // Track if we're editing a job
        let unassignedJobs = []; // Track jobs that couldn't be assigned due to limits
        let settings = {
            workloadBalance: 0.5,
            recentPenalty: 0.3,   // Reduced from 0.6 - was too harsh
            randomness: 0.8,      // Increased from 0.3 - more variety
            historyWeeks: 3,
            splitThreshold: 8,
            maxPointsPerDay: 10,  // New: Global daily limit
            dailyBalanceStrictness: 0.7  // New: How strictly to enforce daily balance
        };

        // Initialize the application
        function init() {
            loadData();
            updateUI();
            setupEventListeners();
        }

        // Setup event listeners for sliders and inputs
        function setupEventListeners() {
            // Points slider
            document.getElementById('job-points').addEventListener('input', function() {
                document.getElementById('points-value').textContent = this.value;
            });

            // Settings sliders
            document.getElementById('workload-balance').addEventListener('input', function() {
                settings.workloadBalance = parseFloat(this.value);
                document.getElementById('workload-value').textContent = this.value;
                saveData();
            });

            document.getElementById('recent-penalty').addEventListener('input', function() {
                document.getElementById('recent-value').textContent = this.value;
            });

            document.getElementById('randomness').addEventListener('input', function() {
                document.getElementById('randomness-value').textContent = this.value;
            });

            document.getElementById('history-weeks').addEventListener('input', function() {
                document.getElementById('history-value').textContent = this.value;
            });

            document.getElementById('split-threshold').addEventListener('input', function() {
                document.getElementById('split-value').textContent = this.value;
            });
            
            // New daily balance sliders
            document.getElementById('max-points-per-day').addEventListener('input', function() {
                document.getElementById('max-points-value').textContent = this.value;
            });
            
            document.getElementById('daily-balance-strictness').addEventListener('input', function() {
                document.getElementById('daily-strictness-value').textContent = this.value;
            });

            // Wizard scoring listeners
            ['time-effort', 'physical-effort', 'unpleasantness'].forEach(id => {
                document.getElementById(id).addEventListener('change', updateWizardScore);
            });
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // Update UI when switching tabs
            if (tabName === 'family') updateFamilyList();
            if (tabName === 'jobs') updateJobsList();
            if (tabName === 'history') updateHistoryList();
            if (tabName === 'jobs') updateExemptionsList();
            if (tabName === 'jobs') updatePreJobsList();
        }

        // Data persistence
        function saveData() {
            localStorage.setItem('choreFamily', JSON.stringify(familyMembers));
            localStorage.setItem('choreJobs', JSON.stringify(jobs));
            localStorage.setItem('choreHistory', JSON.stringify(history));
            localStorage.setItem('choreSettings', JSON.stringify(settings));
        }

        function loadData() {
            try {
                const savedFamily = localStorage.getItem('choreFamily');
                if (savedFamily) familyMembers = JSON.parse(savedFamily);

                const savedJobs = localStorage.getItem('choreJobs');
                if (savedJobs) jobs = JSON.parse(savedJobs);

                const savedHistory = localStorage.getItem('choreHistory');
                if (savedHistory) history = JSON.parse(savedHistory);

                const savedSettings = localStorage.getItem('choreSettings');
                if (savedSettings) {
                    const parsed = JSON.parse(savedSettings);
                    settings = { ...settings, ...parsed };
                }
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        // Family member management
        function addFamilyMember() {
            const nameInput = document.getElementById('family-name');
            const name = nameInput.value.trim();

            if (!name) {
                alert('Please enter a name');
                return;
            }

            if (familyMembers.some(member => member.name.toLowerCase() === name.toLowerCase())) {
                alert('A family member with this name already exists');
                return;
            }

            const member = {
                id: Date.now(),
                name: name,
                addedDate: new Date().toISOString(),
                limitType: 'points', // Default to points-based limits
                dailyLimits: {
                    'Monday': 10, 'Tuesday': 10, 'Wednesday': 10, 'Thursday': 10,
                    'Friday': 10, 'Saturday': 12, 'Sunday': 12
                },
                dailyJobLimits: {
                    'Monday': 5, 'Tuesday': 5, 'Wednesday': 5, 'Thursday': 5,
                    'Friday': 5, 'Saturday': 7, 'Sunday': 7
                }
            };

            familyMembers.push(member);
            nameInput.value = '';
            saveData();
            updateFamilyList();
            updateExemptionsList();
        }

        function removeFamilyMember(id) {
            if (confirm('Are you sure you want to remove this family member?')) {
                familyMembers = familyMembers.filter(member => member.id !== id);
                
                // Remove from job exemptions
                jobs.forEach(job => {
                    job.exemptPeople = job.exemptPeople.filter(exemptId => exemptId !== id);
                });
                
                saveData();
                updateFamilyList();
                updateExemptionsList();
            }
        }

        function updateFamilyList() {
            const listContainer = document.getElementById('family-list');
            
            if (familyMembers.length === 0) {
                listContainer.innerHTML = '<div class="empty-state"><h3>No family members added yet</h3><p>Add family members to start assigning chores!</p></div>';
                return;
            }

            listContainer.innerHTML = familyMembers.map(member => {
                // Ensure member has dailyLimits (for backwards compatibility)
                if (!member.dailyLimits) {
                    member.dailyLimits = {
                        'Monday': 10, 'Tuesday': 10, 'Wednesday': 10, 'Thursday': 10,
                        'Friday': 10, 'Saturday': 12, 'Sunday': 12
                    };
                }
                
                const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                
                return `
                    <div class="family-member" style="margin-bottom: 25px;">
                        <h4>${member.name}</h4>
                        <p>Added: ${new Date(member.addedDate).toLocaleDateString()}</p>
                        
                        <div style="margin: 15px 0;">
                            <h5>Daily Point Limits</h5>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; margin: 10px 0;">
                                ${days.map(day => `
                                    <div style="text-align: center;">
                                        <label style="font-size: 12px; color: #666;">${day.slice(0,3)}</label>
                                        <input type="number" 
                                               value="${member.dailyLimits[day]}" 
                                               min="1" max="20" 
                                               style="width: 60px; padding: 4px; text-align: center; border: 1px solid #ddd; border-radius: 4px;"
                                               onchange="updateDailyLimit(${member.id}, '${day}', this.value)">
                                    </div>
                                `).join('')}
                            </div>
                            
                            <div style="display: flex; gap: 10px; margin-top: 10px; flex-wrap: wrap;">
                                <button class="btn btn-small" onclick="applySchedulePreset(${member.id}, 'work')">Work Schedule</button>
                                <button class="btn btn-small" onclick="applySchedulePreset(${member.id}, 'school')">School Schedule</button>
                                <button class="btn btn-small" onclick="applySchedulePreset(${member.id}, 'free')">Free Schedule</button>
                                <button class="btn btn-small" onclick="applySchedulePreset(${member.id}, 'weekend')">Weekend Worker</button>
                            </div>
                        </div>
                        
                        <button class="btn btn-danger btn-small" onclick="removeFamilyMember(${member.id})">Remove</button>
                    </div>
                `;
            }).join('');
        }

        // Daily limit management
        function updateDailyLimit(memberId, day, value) {
            const member = familyMembers.find(m => m.id === memberId);
            if (member) {
                if (!member.dailyLimits) member.dailyLimits = {};
                member.dailyLimits[day] = parseInt(value) || 1;
                saveData();
            }
        }
        
        function applySchedulePreset(memberId, presetType) {
            const member = familyMembers.find(m => m.id === memberId);
            if (!member) return;
            
            if (!member.dailyLimits) member.dailyLimits = {};
            
            const presets = {
                work: {
                    'Monday': 3, 'Tuesday': 3, 'Wednesday': 3, 'Thursday': 3, 'Friday': 3,
                    'Saturday': 12, 'Sunday': 10
                },
                school: {
                    'Monday': 2, 'Tuesday': 2, 'Wednesday': 2, 'Thursday': 2, 'Friday': 2,
                    'Saturday': 10, 'Sunday': 8
                },
                free: {
                    'Monday': 12, 'Tuesday': 12, 'Wednesday': 12, 'Thursday': 12, 'Friday': 12,
                    'Saturday': 12, 'Sunday': 12
                },
                weekend: {
                    'Monday': 2, 'Tuesday': 2, 'Wednesday': 2, 'Thursday': 2, 'Friday': 2,
                    'Saturday': 15, 'Sunday': 15
                }
            };
            
            if (presets[presetType]) {
                Object.assign(member.dailyLimits, presets[presetType]);
                saveData();
                updateFamilyList();
            }
        }

        // Job management
        function selectScoringMethod(method) {
            document.querySelectorAll('.radio-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            document.querySelector(`input[value="${method}"]`).checked = true;
            
            if (method === 'manual') {
                document.getElementById('manual-scoring').style.display = 'block';
                document.getElementById('wizard-scoring').style.display = 'none';
            } else {
                document.getElementById('manual-scoring').style.display = 'none';
                document.getElementById('wizard-scoring').style.display = 'block';
                updateWizardScore();
            }
        }

        function updateWizardScore() {
            const time = parseInt(document.getElementById('time-effort').value);
            const physical = parseInt(document.getElementById('physical-effort').value);
            const unpleasant = parseInt(document.getElementById('unpleasantness').value);
            
            const score = Math.min(10, Math.max(1, time + physical + unpleasant - 1));
            
            document.getElementById('wizard-calculation').innerHTML = `
                <div class="balance-summary">
                    <h4>Calculated Score: ${score}/10</h4>
                    <p>Time (${time}) + Physical (${physical}) + Unpleasantness (${unpleasant}) - 1 = ${score}</p>
                </div>
            `;
        }

        function toggleExemptions() {
            const checkbox = document.getElementById('has-exemptions');
            const list = document.getElementById('exemptions-list');
            
            if (checkbox.checked) {
                list.style.display = 'block';
                updateExemptionsList();
            } else {
                list.style.display = 'none';
            }
        }

        function updateExemptionsList() {
            const container = document.getElementById('exemptions-list');
            if (!container || container.style.display === 'none') return;
            
            if (familyMembers.length === 0) {
                container.innerHTML = '<p>Add family members first</p>';
                return;
            }

            container.innerHTML = `
                <label style="margin-top: 10px; margin-bottom: 10px; display: block;">Who should NEVER do this job:</label>
                ${familyMembers.map(member => `
                    <div class="form-check">
                        <input type="checkbox" id="exempt-${member.id}" value="${member.id}">
                        <label for="exempt-${member.id}">${member.name}</label>
                    </div>
                `).join('')}
            `;
        }

        function toggleDependency() {
            const checkbox = document.getElementById('has-dependency');
            const controls = document.getElementById('dependency-controls');
            
            if (checkbox.checked) {
                controls.style.display = 'block';
                updatePreJobsList();
            } else {
                controls.style.display = 'none';
            }
        }

        function updatePreJobsList() {
            const select = document.getElementById('pre-job');
            if (!select) return;
            
            select.innerHTML = '<option value="">Select a job...</option>';
            
            jobs.forEach(job => {
                select.innerHTML += `<option value="${job.id}">${job.name}</option>`;
            });
        }

        function editJob(id) {
            const job = jobs.find(j => j.id === id);
            if (!job) return;
            
            editingJobId = id;
            
            // Fill form with job data
            document.getElementById('job-name').value = job.name;
            document.getElementById('job-frequency').value = job.frequency;
            
            // Set scoring method
            document.getElementById('job-points').value = job.points;
            document.getElementById('points-value').textContent = job.points;
            
            // Set exemptions
            document.getElementById('has-exemptions').checked = job.exemptPeople.length > 0;
            toggleExemptions();
            if (job.exemptPeople.length > 0) {
                job.exemptPeople.forEach(personId => {
                    const checkbox = document.getElementById(`exempt-${personId}`);
                    if (checkbox) checkbox.checked = true;
                });
            }
            
            // Set dependencies
            document.getElementById('has-dependency').checked = job.requiresPreJob;
            toggleDependency();
            if (job.requiresPreJob) {
                document.getElementById('pre-job').value = job.preJobId;
                document.getElementById('pre-job-timing').value = job.preJobTiming;
            }
            
            // Update UI for edit mode
            document.getElementById('job-submit-btn').textContent = 'Update Job';
            document.getElementById('job-cancel-btn').style.display = 'inline-block';
            
            // Scroll to form
            document.querySelector('#jobs .card').scrollIntoView({ behavior: 'smooth' });
        }
        
        function cancelJobEdit() {
            editingJobId = null;
            
            // Reset form
            document.getElementById('job-name').value = '';
            document.getElementById('job-points').value = 5;
            document.getElementById('points-value').textContent = '5';
            document.getElementById('job-frequency').value = 'Daily';
            document.getElementById('has-exemptions').checked = false;
            document.getElementById('has-dependency').checked = false;
            toggleExemptions();
            toggleDependency();
            
            // Reset UI
            document.getElementById('job-submit-btn').textContent = 'Add Job';
            document.getElementById('job-cancel-btn').style.display = 'none';
        }

        function addJob() {
            const name = document.getElementById('job-name').value.trim();
            const frequency = document.getElementById('job-frequency').value;
            
            if (!name) {
                alert('Please enter a job name');
                return;
            }

            // Check for duplicate names (excluding current job if editing)
            const existingJob = jobs.find(job => 
                job.name.toLowerCase() === name.toLowerCase() && 
                job.id !== editingJobId
            );
            if (existingJob) {
                alert('A job with this name already exists');
                return;
            }

            // Get points
            let points;
            const scoringMethod = document.querySelector('input[name="scoring"]:checked').value;
            if (scoringMethod === 'manual') {
                points = parseInt(document.getElementById('job-points').value);
            } else {
                const time = parseInt(document.getElementById('time-effort').value);
                const physical = parseInt(document.getElementById('physical-effort').value);
                const unpleasant = parseInt(document.getElementById('unpleasantness').value);
                points = Math.min(10, Math.max(1, time + physical + unpleasant - 1));
            }

            // Get exemptions
            const exemptPeople = [];
            if (document.getElementById('has-exemptions').checked) {
                familyMembers.forEach(member => {
                    const checkbox = document.getElementById(`exempt-${member.id}`);
                    if (checkbox && checkbox.checked) {
                        exemptPeople.push(member.id);
                    }
                });
            }

            // Get dependency
            let requiresPreJob = false;
            let preJobId = null;
            let preJobTiming = 'same-day';
            
            if (document.getElementById('has-dependency').checked) {
                const preJob = document.getElementById('pre-job').value;
                if (preJob) {
                    requiresPreJob = true;
                    preJobId = parseInt(preJob);
                    preJobTiming = document.getElementById('pre-job-timing').value;
                }
            }

            if (editingJobId) {
                // Update existing job
                const jobIndex = jobs.findIndex(j => j.id === editingJobId);
                if (jobIndex !== -1) {
                    jobs[jobIndex] = {
                        ...jobs[jobIndex],
                        name: name,
                        points: points,
                        frequency: frequency,
                        exemptPeople: exemptPeople,
                        requiresPreJob: requiresPreJob,
                        preJobId: preJobId,
                        preJobTiming: preJobTiming
                    };
                }
                
                // Exit edit mode
                cancelJobEdit();
            } else {
                // Add new job
                const job = {
                    id: Date.now(),
                    name: name,
                    points: points,
                    frequency: frequency,
                    exemptPeople: exemptPeople,
                    requiresPreJob: requiresPreJob,
                    preJobId: preJobId,
                    preJobTiming: preJobTiming,
                    addedDate: new Date().toISOString()
                };

                jobs.push(job);
                
                // Reset form
                document.getElementById('job-name').value = '';
                document.getElementById('has-exemptions').checked = false;
                document.getElementById('has-dependency').checked = false;
                toggleExemptions();
                toggleDependency();
            }
            
            saveData();
            updateJobsList();
            updatePreJobsList();
        }

        function removeJob(id) {
            if (confirm('Are you sure you want to remove this job?')) {
                // If we're editing this job, exit edit mode
                if (editingJobId === id) {
                    cancelJobEdit();
                }
                
                jobs = jobs.filter(job => job.id !== id);
                
                // Remove dependencies on this job
                jobs.forEach(job => {
                    if (job.preJobId === id) {
                        job.requiresPreJob = false;
                        job.preJobId = null;
                        job.preJobTiming = 'same-day';
                    }
                });
                
                saveData();
                updateJobsList();
                updatePreJobsList();
            }
        }

        function updateJobsList() {
            const listContainer = document.getElementById('jobs-list');
            
            if (jobs.length === 0) {
                listContainer.innerHTML = '<div class="empty-state"><h3>No jobs added yet</h3><p>Add jobs to start generating rosters!</p></div>';
                return;
            }

            listContainer.innerHTML = jobs.map(job => {
                const difficulty = job.points <= 3 ? 'easy' : job.points <= 7 ? 'medium' : 'hard';
                const exemptNames = job.exemptPeople.map(id => {
                    const member = familyMembers.find(m => m.id === id);
                    return member ? member.name : 'Unknown';
                }).join(', ');
                
                const preJob = job.requiresPreJob && job.preJobId ? jobs.find(j => j.id === job.preJobId) : null;
                
                let subtitle = `${job.frequency}`;
                if (exemptNames) subtitle += ` • Exempt: ${exemptNames}`;
                if (preJob) subtitle += ` • Requires: ${preJob.name} (${job.preJobTiming})`;
                
                return `
                    <div class="job-item">
                        <h4>${job.name} <span class="job-points ${difficulty}">${job.points}</span></h4>
                        <p>${subtitle}</p>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-small" onclick="editJob(${job.id})">Edit</button>
                            <button class="btn btn-danger btn-small" onclick="removeJob(${job.id})">Remove</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Settings management
        function saveSettings() {
            settings.workloadBalance = parseFloat(document.getElementById('workload-balance').value);
            settings.recentPenalty = parseFloat(document.getElementById('recent-penalty').value);
            settings.randomness = parseFloat(document.getElementById('randomness').value);
            settings.historyWeeks = parseInt(document.getElementById('history-weeks').value);
            settings.splitThreshold = parseInt(document.getElementById('split-threshold').value);
            settings.maxPointsPerDay = parseInt(document.getElementById('max-points-per-day').value);
            settings.dailyBalanceStrictness = parseFloat(document.getElementById('daily-balance-strictness').value);
            
            saveData();
            alert('Settings saved!');
        }

        function resetSettings() {
            if (confirm('Reset all settings to defaults?')) {
                settings = {
                    workloadBalance: 0.5,
                    recentPenalty: 0.3,   // Improved default
                    randomness: 0.8,      // Improved default
                    historyWeeks: 3,
                    splitThreshold: 8,
                    maxPointsPerDay: 10,
                    dailyBalanceStrictness: 0.7
                };
                
                updateSettingsUI();
                saveData();
                alert('Settings reset to defaults!');
            }
        }

        function updateSettingsUI() {
            document.getElementById('workload-balance').value = settings.workloadBalance;
            document.getElementById('workload-value').textContent = settings.workloadBalance;
            
            document.getElementById('recent-penalty').value = settings.recentPenalty;
            document.getElementById('recent-value').textContent = settings.recentPenalty;
            
            document.getElementById('randomness').value = settings.randomness;
            document.getElementById('randomness-value').textContent = settings.randomness;
            
            document.getElementById('history-weeks').value = settings.historyWeeks;
            document.getElementById('history-value').textContent = settings.historyWeeks;
            
            document.getElementById('split-threshold').value = settings.splitThreshold;
            document.getElementById('split-value').textContent = settings.splitThreshold;
            
            document.getElementById('max-points-per-day').value = settings.maxPointsPerDay;
            document.getElementById('max-points-value').textContent = settings.maxPointsPerDay;
            
            document.getElementById('daily-balance-strictness').value = settings.dailyBalanceStrictness;
            document.getElementById('daily-strictness-value').textContent = settings.dailyBalanceStrictness;
        }

        // Roster generation
        function generateRoster() {
            if (familyMembers.length === 0) {
                alert('Please add family members first');
                return;
            }

            if (jobs.length === 0) {
                alert('Please add some jobs first');
                return;
            }

            const roster = createWeeklyRoster();
            const rosterEntry = {
                id: Date.now(),
                date: new Date().toISOString(),
                weekStarting: getWeekStartDate(),
                roster: roster
            };

            // Add to history (keep only last 5)
            history.unshift(rosterEntry);
            if (history.length > 5) {
                history = history.slice(0, 5);
            }

            saveData();
            displayRoster(roster);
        }

        function createWeeklyRoster() {
            const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            const roster = {};
            
            // Clear unassigned jobs from previous generation
            unassignedJobs = [];
            
            // Initialize roster structure
            familyMembers.forEach(member => {
                roster[member.name] = {};
                days.forEach(day => {
                    roster[member.name][day] = [];
                });
            });

            // Generate job instances for the week, grouped by job type
            const jobGroups = new Map();
            
            jobs.forEach(job => {
                const instances = getJobInstances(job);
                jobGroups.set(job.id, {
                    job: job,
                    instances: instances.map(instance => ({
                        ...job,
                        assignedDay: instance.day,
                        dayIndex: instance.dayIndex,
                        originalJobId: job.id
                    }))
                });
            });

            // Process jobs by type: daily jobs need special handling
            const dailyJobs = Array.from(jobGroups.values()).filter(group => group.job.frequency === 'Daily');
            const otherJobs = Array.from(jobGroups.values()).filter(group => group.job.frequency !== 'Daily');
            
            // Assign daily jobs with smart distribution
            dailyJobs.forEach(group => {
                if (!group.job.requiresPreJob) {
                    assignDailyJobGroup(roster, group, days);
                }
            });
            
            // Assign other jobs normally, but update current week state as we go
            const otherJobInstances = [];
            otherJobs.forEach(group => {
                group.instances.forEach(instance => {
                    if (!instance.requiresPreJob) {
                        otherJobInstances.push(instance);
                    }
                });
            });
            
            // Sort other jobs by points (highest first) but add some randomness
            otherJobInstances.sort((a, b) => {
                const pointDiff = b.points - a.points;
                const randomFactor = (Math.random() - 0.5) * 2 * settings.randomness;
                return pointDiff + randomFactor;
            });
            
            // Assign other jobs with current week awareness
            otherJobInstances.forEach(job => {
                assignJobWithCurrentWeekAwareness(roster, job, days);
            });

            // Finally, assign dependent jobs
            const dependentInstances = [];
            Array.from(jobGroups.values()).forEach(group => {
                group.instances.forEach(instance => {
                    if (instance.requiresPreJob) {
                        dependentInstances.push(instance);
                    }
                });
            });
            
            dependentInstances.forEach(job => {
                assignDependentJobWithAwareness(roster, job, days);
            });

            return roster;
        }

        function getJobInstances(job) {
            const instances = [];
            const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            
            switch (job.frequency) {
                case 'Daily':
                    days.forEach((day, index) => {
                        instances.push({ day, dayIndex: index });
                    });
                    break;
                case 'Every 2 days':
                    [0, 2, 4, 6].forEach(index => { // Mon, Wed, Fri, Sun
                        instances.push({ day: days[index], dayIndex: index });
                    });
                    break;
                case 'Every 3 days':
                    [0, 3].forEach(index => { // Mon, Thu
                        instances.push({ day: days[index], dayIndex: index });
                    });
                    break;
                case 'Weekly':
                    const randomDay = Math.floor(Math.random() * 7);
                    instances.push({ day: days[randomDay], dayIndex: randomDay });
                    break;
                case 'Monthly':
                    const weekendDay = Math.random() < 0.5 ? 5 : 6; // Sat or Sun
                    instances.push({ day: days[weekendDay], dayIndex: weekendDay });
                    break;
            }
            
            return instances;
        }

        // Smart daily job assignment - spreads jobs across multiple people
        function assignDailyJobGroup(roster, jobGroup, days) {
            const job = jobGroup.job;
            const instances = jobGroup.instances; // All 7 days
            
            const availablePeople = familyMembers.filter(member => 
                !job.exemptPeople.includes(member.id)
            );

            if (availablePeople.length === 0) {
                console.warn(`No one can do job: ${job.name}`);
                return;
            }

            // Strategy: Assign 1-3 consecutive days to each person, then switch
            const assignments = [];
            let currentPersonIndex = 0;
            let consecutiveDays = 0;
            const maxConsecutive = Math.min(3, Math.max(1, Math.floor(7 / availablePeople.length)));
            
            for (let dayIndex = 0; dayIndex < instances.length; dayIndex++) {
                const instance = instances[dayIndex];
                
                // Pick best person for this day considering current week state
                const scores = availablePeople.map(person => {
                    return calculateImprovedScore(roster, person, job, instance.assignedDay);
                });
                
                scores.sort((a, b) => b.score - a.score);
                let selectedPerson = scores[0].person;
                
                // Apply consecutive day logic for variety
                if (consecutiveDays >= maxConsecutive || (dayIndex > 0 && Math.random() < 0.3)) {
                    // Switch to different person if available
                    const differentPerson = scores.find(s => s.person.id !== selectedPerson.id);
                    if (differentPerson && differentPerson.score > 0.5) { // Only switch if reasonable score
                        selectedPerson = differentPerson.person;
                        consecutiveDays = 0;
                    }
                }
                
                // Assign the job
                roster[selectedPerson.name][instance.assignedDay].push({
                    id: job.id,
                    name: job.name,
                    points: job.points
                });
                
                consecutiveDays++;
            }
        }
        
        // Improved job assignment with current week awareness
        function assignJobWithCurrentWeekAwareness(roster, job, days) {
            const availablePeople = familyMembers.filter(member => 
                !job.exemptPeople.includes(member.id)
            );

            if (availablePeople.length === 0) {
                console.warn(`No one can do job: ${job.name}`);
                unassignedJobs.push({ ...job, reason: 'No available people (all exempt)' });
                return;
            }

            // Calculate improved scores for each person
            const scores = availablePeople.map(person => {
                return calculateImprovedScore(roster, person, job, job.assignedDay);
            }).filter(s => s.score >= 0); // Remove people who exceed daily limits

            if (scores.length === 0) {
                console.warn(`No one can do job due to daily limits: ${job.name}`);
                unassignedJobs.push({ ...job, reason: 'Daily limits exceeded for all available people' });
                return;
            }

            // Pick the person with the highest score
            scores.sort((a, b) => b.score - a.score);
            const selectedPerson = scores[0].person;

            // Assign the job
            roster[selectedPerson.name][job.assignedDay].push({
                id: job.id,
                name: job.name,
                points: job.points
            });
        }
        
        // Calculate improved fairness score with daily limit checking
        function calculateImprovedScore(roster, person, job, assignedDay) {
            // Check daily limits first - if this would exceed limit, return -1 (invalid)
            const currentDayPoints = getCurrentDayPoints(roster, person.name, assignedDay);
            const personalLimit = person.dailyLimits ? person.dailyLimits[assignedDay] || 10 : 10;
            const globalLimit = settings.maxPointsPerDay;
            const effectiveLimit = Math.min(personalLimit, globalLimit);
            
            if (currentDayPoints + job.points > effectiveLimit) {
                return { person, score: -1 }; // Invalid due to daily limit
            }
            
            // Start with base score of 1.0 (always positive)
            let score = 1.0;
            
            // Get current week points INCLUDING jobs assigned so far
            const currentWeekPoints = getCurrentWeekPoints(roster, person.name);
            const averageCurrentPoints = getAveragePoints(roster);
            
            // Workload balance bonus (people with fewer points get bonus)
            const maxJobPoints = Math.max(...jobs.map(j => j.points), 10);
            const workloadDifference = averageCurrentPoints - currentWeekPoints;
            const workloadBonus = Math.max(0, workloadDifference / maxJobPoints * settings.workloadBalance);
            score += workloadBonus;
            
            // Recent job history bonus (fewer recent = bonus)
            const recentJobCount = getRecentJobCount(person.name, job.name);
            const maxRecentJobs = settings.historyWeeks * 2; // Reasonable max
            const recentJobBonus = Math.max(0, (maxRecentJobs - recentJobCount) / maxRecentJobs * settings.recentPenalty);
            score += recentJobBonus;
            
            // Current week job variety bonus
            const currentWeekJobCount = getCurrentWeekJobCount(roster, person.name, job.name);
            const varietyBonus = Math.max(0, (2 - currentWeekJobCount) * 0.5); // Bonus for not having this job this week
            score += varietyBonus;
            
            // Daily balance bonus (people with fewer points today get bonus)
            const averageDayPoints = familyMembers.reduce((sum, member) => {
                return sum + getCurrentDayPoints(roster, member.name, assignedDay);
            }, 0) / familyMembers.length;
            const dailyDifference = averageDayPoints - currentDayPoints;
            const dailyBonus = Math.max(0, dailyDifference / maxJobPoints * settings.dailyBalanceStrictness);
            score += dailyBonus;
            
            // Apply randomness as multiplier
            const randomMultiplier = 1 + (Math.random() - 0.5) * settings.randomness;
            score *= Math.max(0.1, randomMultiplier); // Never go below 0.1
            
            return { person, score };
        }
        
        // Get how many times person has done this specific job this week so far
        function getCurrentWeekJobCount(roster, personName, jobName) {
            let count = 0;
            if (roster[personName]) {
                Object.values(roster[personName]).forEach(dayJobs => {
                    count += dayJobs.filter(job => job.name === jobName).length;
                });
            }
            return count;
        }
        
        // Get total points for a person on a specific day
        function getCurrentDayPoints(roster, personName, dayName) {
            if (!roster[personName] || !roster[personName][dayName]) {
                return 0;
            }
            return roster[personName][dayName].reduce((sum, job) => sum + job.points, 0);
        }

        function assignDependentJobWithAwareness(roster, job, days) {
            const preJob = jobs.find(j => j.id === job.preJobId);
            if (!preJob) {
                assignJobWithCurrentWeekAwareness(roster, job, days); // Fallback
                return;
            }

            // Find who did the pre-job and when
            let preJobAssignments = [];
            familyMembers.forEach(member => {
                days.forEach((day, dayIndex) => {
                    const dayJobs = roster[member.name][day];
                    const hasPreJob = dayJobs.some(j => j.id === preJob.id);
                    if (hasPreJob) {
                        preJobAssignments.push({ person: member, day, dayIndex });
                    }
                });
            });

            if (preJobAssignments.length === 0) {
                assignJobWithCurrentWeekAwareness(roster, job, days); // Fallback
                return;
            }

            // Filter valid assignments based on timing
            const validAssignments = preJobAssignments.filter(assignment => {
                if (job.preJobTiming === 'same-day') {
                    return assignment.day === job.assignedDay;
                } else if (job.preJobTiming === 'next-day') {
                    return assignment.dayIndex === days.indexOf(job.assignedDay) - 1;
                } else { // flexible
                    return assignment.dayIndex <= days.indexOf(job.assignedDay);
                }
            });

            if (validAssignments.length === 0) {
                assignJobWithCurrentWeekAwareness(roster, job, days); // Fallback
                return;
            }

            // Smart pairing logic with improved scoring
            const totalPoints = job.points + preJob.points;
            const shouldSplitJobs = totalPoints >= settings.splitThreshold;

            let targetPerson;
            if (shouldSplitJobs) {
                // Try to assign to different people using improved scoring
                const availablePeople = familyMembers.filter(member => 
                    !job.exemptPeople.includes(member.id) &&
                    !validAssignments.some(assignment => assignment.person.id === member.id)
                );

                if (availablePeople.length > 0) {
                    // Use improved scoring to pick best person
                    const scores = availablePeople.map(person => {
                        return calculateImprovedScore(roster, person, job, job.assignedDay);
                    });
                    scores.sort((a, b) => b.score - a.score);
                    targetPerson = scores[0].person;
                } else {
                    // Must assign to someone who did the pre-job, pick best one
                    const scores = validAssignments.map(assignment => {
                        if (job.exemptPeople.includes(assignment.person.id)) {
                            return { person: assignment.person, score: -1 }; // Invalid
                        }
                        return calculateImprovedScore(roster, assignment.person, job, job.assignedDay);
                    }).filter(s => s.score >= 0);
                    
                    if (scores.length > 0) {
                        scores.sort((a, b) => b.score - a.score);
                        targetPerson = scores[0].person;
                    } else {
                        assignJobWithCurrentWeekAwareness(roster, job, days); // Fallback
                        return;
                    }
                }
            } else {
                // Allow same person for small job combinations, but pick best valid assignment
                const scores = validAssignments.map(assignment => {
                    if (job.exemptPeople.includes(assignment.person.id)) {
                        return { person: assignment.person, score: -1 }; // Invalid
                    }
                    return calculateImprovedScore(roster, assignment.person, job, job.assignedDay);
                }).filter(s => s.score >= 0);
                
                if (scores.length > 0) {
                    scores.sort((a, b) => b.score - a.score);
                    targetPerson = scores[0].person;
                } else {
                    assignJobWithCurrentWeekAwareness(roster, job, days); // Fallback
                    return;
                }
            }

            // Assign the dependent job
            roster[targetPerson.name][job.assignedDay].push({
                id: job.id,
                name: job.name,
                points: job.points
            });
        }

        function getRecentJobCount(personName, jobName) {
            let count = 0;
            const recentHistory = history.slice(0, settings.historyWeeks);
            
            recentHistory.forEach(entry => {
                if (entry.roster[personName]) {
                    Object.values(entry.roster[personName]).forEach(dayJobs => {
                        count += dayJobs.filter(job => job.name === jobName).length;
                    });
                }
            });
            
            return count;
        }

        function getCurrentWeekPoints(roster, personName) {
            let points = 0;
            if (roster[personName]) {
                Object.values(roster[personName]).forEach(dayJobs => {
                    points += dayJobs.reduce((sum, job) => sum + job.points, 0);
                });
            }
            return points;
        }

        function getAveragePoints(roster) {
            const personPoints = familyMembers.map(member => getCurrentWeekPoints(roster, member.name));
            return personPoints.reduce((sum, points) => sum + points, 0) / personPoints.length;
        }

        function displayRoster(roster) {
            const container = document.getElementById('roster-results');
            const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            
            // Calculate balance metrics
            const personPoints = familyMembers.map(member => ({
                name: member.name,
                points: getCurrentWeekPoints(roster, member.name)
            }));
            
            const totalPoints = personPoints.reduce((sum, p) => sum + p.points, 0);
            const averagePoints = totalPoints / personPoints.length;
            const maxDeviation = Math.max(...personPoints.map(p => Math.abs(p.points - averagePoints)));
            const fairnessScore = Math.max(0, 100 - (maxDeviation / averagePoints * 100));

            container.innerHTML = `
                <div class="balance-summary">
                    <h3>📊 Balance Summary</h3>
                    <div class="balance-stats">
                        <div class="stat">
                            <div class="stat-value">${totalPoints}</div>
                            <div class="stat-label">Total Points</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${averagePoints.toFixed(1)}</div>
                            <div class="stat-label">Average Points</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${fairnessScore.toFixed(0)}%</div>
                            <div class="stat-label">Fairness Score</div>
                        </div>
                    </div>
                </div>
                
                <div class="no-print" style="margin-bottom: 20px;">
                    <button class="btn" onclick="window.print()">🖨️ Print Weekly Roster</button>
                    ${unassignedJobs.length > 0 ? 
                        '<button class="btn btn-secondary" onclick="retryWithRelaxedLimits()" style="margin-left: 10px;">🔄 Retry with Relaxed Limits</button>' 
                        : ''
                    }
                </div>
                
                ${unassignedJobs.length > 0 ? `
                    <div class="card" style="border-left: 4px solid #dc3545; background-color: #fff5f5;">
                        <h3 style="color: #dc3545;">⚠️ Unassigned Jobs (${unassignedJobs.length})</h3>
                        <p style="margin-bottom: 15px;">These jobs couldn't be assigned due to daily limit restrictions:</p>
                        ${unassignedJobs.map(job => {
                            const difficulty = job.points <= 3 ? 'easy' : job.points <= 7 ? 'medium' : 'hard';
                            return `
                                <div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 6px; border-left: 3px solid #dc3545;">
                                    <strong>${job.name}</strong> 
                                    <span class="job-points ${difficulty}">${job.points}</span>
                                    <span style="color: #666;">on ${job.assignedDay}</span>
                                    <br><small style="color: #666;">${job.reason}</small>
                                </div>
                            `;
                        }).join('')}
                        <p style="margin-top: 15px; font-size: 14px; color: #666;">
                            💡 Try: Relaxing daily limits, adjusting job points, or manually assigning these jobs to weekend days.
                        </p>
                    </div>
                ` : ''}
                
                <div class="roster-grid">
                    ${familyMembers.map(member => {
                        const totalPoints = getCurrentWeekPoints(roster, member.name);
                        const difficulty = totalPoints <= 10 ? 'easy' : totalPoints <= 20 ? 'medium' : 'hard';
                        
                        return `
                            <div class="person-card">
                                <div class="person-header">
                                    <div class="person-name">${member.name}</div>
                                    <div class="person-points ${difficulty}">${totalPoints} pts</div>
                                </div>
                                ${days.map(day => {
                                    const dayJobs = roster[member.name][day] || [];
                                    return `
                                        <div class="day-schedule">
                                            <div class="day-name">${day}</div>
                                            <div class="day-jobs">
                                                ${dayJobs.length === 0 ? 
                                                    '<span style="color: #999; font-style: italic;">No jobs</span>' :
                                                    dayJobs.map(job => {
                                                        const jobDifficulty = job.points <= 3 ? 'easy' : job.points <= 7 ? 'medium' : 'hard';
                                                        return `<span class="job-tag ${jobDifficulty}">${job.name}</span>`;
                                                    }).join('')
                                                }
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }

        function getWeekStartDate() {
            const today = new Date();
            const day = today.getDay();
            const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
            const monday = new Date(today.setDate(diff));
            return monday.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
        
        // Retry roster generation with temporarily relaxed daily limits
        function retryWithRelaxedLimits() {
            if (confirm('This will temporarily increase everyone\'s daily limits by +3 points and regenerate the roster. Continue?')) {
                // Store original limits
                const originalLimits = {};
                familyMembers.forEach(member => {
                    originalLimits[member.id] = { ...member.dailyLimits };
                    // Increase all daily limits by 3 points
                    Object.keys(member.dailyLimits).forEach(day => {
                        member.dailyLimits[day] += 3;
                    });
                });
                
                // Store original global limit
                const originalGlobalLimit = settings.maxPointsPerDay;
                settings.maxPointsPerDay += 3;
                
                // Regenerate roster
                const roster = createWeeklyRoster();
                displayRoster(roster);
                
                // Restore original limits
                familyMembers.forEach(member => {
                    member.dailyLimits = originalLimits[member.id];
                });
                settings.maxPointsPerDay = originalGlobalLimit;
                
                // Show message if still unassigned jobs
                if (unassignedJobs.length > 0) {
                    alert(`Still ${unassignedJobs.length} unassigned jobs. Consider reducing job points or manually setting higher daily limits.`);
                } else {
                    alert('Success! All jobs assigned with relaxed limits. Consider adjusting your daily limits or job points permanently.');
                }
            }
        }

        // History management
        function updateHistoryList() {
            const container = document.getElementById('history-list');
            
            if (history.length === 0) {
                container.innerHTML = '<div class="empty-state"><h3>No roster history</h3><p>Generate your first roster to see it here!</p></div>';
                return;
            }

            container.innerHTML = history.map(entry => `
                <div class="history-item">
                    <div class="history-header">
                        <div class="history-date">Week of ${entry.weekStarting}</div>
                        <div class="history-actions">
                            <button class="btn btn-small" onclick="viewHistoryDetails(${entry.id})">View Details</button>
                            <button class="btn btn-danger btn-small" onclick="deleteHistory(${entry.id})">Delete</button>
                        </div>
                    </div>
                    <p>Generated on ${new Date(entry.date).toLocaleDateString()}</p>
                </div>
            `).join('');
        }

        function viewHistoryDetails(id) {
            const entry = history.find(h => h.id === id);
            if (!entry) return;
            
            displayRoster(entry.roster);
            showTab('generate');
        }

        function deleteHistory(id) {
            if (confirm('Delete this roster from history?')) {
                history = history.filter(h => h.id !== id);
                saveData();
                updateHistoryList();
            }
        }

        // Update all UI elements
        function updateUI() {
            updateFamilyList();
            updateJobsList();
            updateHistoryList();
            updateSettingsUI();
            updateExemptionsList();
            updatePreJobsList();
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>

